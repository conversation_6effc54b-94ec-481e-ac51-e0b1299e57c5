import { Heart, Mail, Phone, MapPin } from "lucide-react"

export default function Footer() {
  return (
    <footer className="bg-gray-900 text-white py-12">
      <div className="container mx-auto px-4">
        <div className="grid md:grid-cols-4 gap-8">
          <div className="md:col-span-2">
            <h3 className="text-2xl font-bold bg-gradient-to-r from-orange-400 to-rose-400 bg-clip-text text-transparent mb-4">
              OrangeRose
            </h3>
            <p className="text-gray-400 mb-6 leading-relaxed">
              Professional event management company specializing in conferences, exhibitions, and corporate events. We
              transform your vision into unforgettable experiences with precision and excellence.
            </p>
            <div className="space-y-2">
              <div className="flex items-center gap-3">
                <Phone className="w-4 h-4 text-orange-400" />
                <span className="text-gray-400">+91 8128338581</span>
              </div>
              <div className="flex items-center gap-3">
                <Mail className="w-4 h-4 text-orange-400" />
                <span className="text-gray-400"><EMAIL></span>
              </div>
              <div className="flex items-center gap-3">
                <MapPin className="w-4 h-4 text-orange-400" />
                <span className="text-gray-400">Serving clients nationwide</span>
              </div>
            </div>
          </div>

          <div>
            <h4 className="font-semibold mb-4">Services</h4>
            <ul className="space-y-2 text-gray-400">
              <li>
                <a href="/services" className="hover:text-orange-400 transition-colors">
                  Conference Management
                </a>
              </li>
              <li>
                <a href="/services" className="hover:text-orange-400 transition-colors">
                  Exhibition Management
                </a>
              </li>
              <li>
                <a href="/services" className="hover:text-orange-400 transition-colors">
                  Corporate Events
                </a>
              </li>
              <li>
                <a href="/services" className="hover:text-orange-400 transition-colors">
                  Speaker Management
                </a>
              </li>
            </ul>
          </div>

          <div>
            <h4 className="font-semibold mb-4">Quick Links</h4>
            <ul className="space-y-2 text-gray-400">
              <li>
                <a href="/about" className="hover:text-orange-400 transition-colors">
                  About Us
                </a>
              </li>
              <li>
                <a href="/our-work" className="hover:text-orange-400 transition-colors">
                  Our Work
                </a>
              </li>
              <li>
                <a href="/projects" className="hover:text-orange-400 transition-colors">
                  Projects
                </a>
              </li>
              <li>
                <a href="/contact" className="hover:text-orange-400 transition-colors">
                  Contact
                </a>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-800 mt-8 pt-8 text-center">
          <p className="text-gray-400 flex items-center justify-center gap-2">
            Made with <Heart className="w-4 h-4 text-rose-400" /> for OrangeRose © 2025. All rights reserved.
          </p>
          <p className="text-gray-500 text-sm mt-2">Professional Event Management | Working Hours: 10am - 6pm</p>
        </div>
      </div>
    </footer>
  )
}
