"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Settings, MessageSquare, Calendar, Award, Plus, Edit, Trash2, Save, Eye, BarChart3 } from "lucide-react"

export default function AdminPanel() {
  const [activeTab, setActiveTab] = useState("dashboard")

  // Sample data - in real app, this would come from a database
  const [testimonials, setTestimonials] = useState([
    {
      id: 1,
      name: "Dr. <PERSON>",
      position: "Chief Medical Officer",
      company: "Global Health Solutions",
      text: "OrangeRose transformed our annual medical conference into an extraordinary experience...",
      rating: 5,
      event: "International Medical Conference 2024",
      category: "Healthcare",
      status: "published",
    },
    {
      id: 2,
      name: "<PERSON>",
      position: "Director of Operations",
      company: "PharmaTech Industries",
      text: "Working with OrangeRose for our pharmaceutical exhibition was a game-changer...",
      rating: 5,
      event: "Pharmaceutical Innovation Expo 2024",
      category: "Pharmaceutical",
      status: "published",
    },
  ])

  const [projects, setProjects] = useState([
    {
      id: 1,
      title: "International Medical Conference 2024",
      category: "Conference",
      description: "A comprehensive medical conference featuring leading healthcare professionals...",
      location: "Mumbai, India",
      date: "March 2024",
      attendees: "500+",
      status: "completed",
    },
    {
      id: 2,
      title: "Pharmaceutical Innovation Expo",
      category: "Exhibition",
      description: "Showcasing the latest innovations in pharmaceutical technology...",
      location: "Delhi, India",
      date: "February 2024",
      attendees: "10,000+",
      status: "completed",
    },
  ])

  const [services, setServices] = useState([
    {
      id: 1,
      title: "Conference Management",
      description: "End-to-end conference planning including venue selection, speaker coordination...",
      features: ["Advisory Board Meetings", "Investigator Meetings", "MICE Programme", "CME Programs"],
      pricing: "Starting from ₹2,50,000",
      status: "active",
    },
    {
      id: 2,
      title: "Exhibition Management",
      description: "Complete exhibition services from concept to execution...",
      features: ["Sales and Marketing", "Operation Management", "Visitor Promotion"],
      pricing: "Starting from ₹5,00,000",
      status: "active",
    },
  ])

  const stats = {
    totalTestimonials: testimonials.length,
    totalProjects: projects.length,
    totalServices: services.length,
    publishedTestimonials: testimonials.filter((t) => t.status === "published").length,
  }

  return (
    <div className="min-h-screen bg-gray-50 pt-32">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Admin Panel</h1>
          <p className="text-gray-600">Manage your website content and settings</p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-5 lg:w-auto lg:grid-cols-5">
            <TabsTrigger value="dashboard" className="flex items-center gap-2">
              <BarChart3 className="w-4 h-4" />
              Dashboard
            </TabsTrigger>
            <TabsTrigger value="testimonials" className="flex items-center gap-2">
              <MessageSquare className="w-4 h-4" />
              Testimonials
            </TabsTrigger>
            <TabsTrigger value="projects" className="flex items-center gap-2">
              <Calendar className="w-4 h-4" />
              Projects
            </TabsTrigger>
            <TabsTrigger value="services" className="flex items-center gap-2">
              <Award className="w-4 h-4" />
              Services
            </TabsTrigger>
            <TabsTrigger value="settings" className="flex items-center gap-2">
              <Settings className="w-4 h-4" />
              Settings
            </TabsTrigger>
          </TabsList>

          {/* Dashboard */}
          <TabsContent value="dashboard" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Total Testimonials</p>
                      <p className="text-3xl font-bold text-gray-900">{stats.totalTestimonials}</p>
                    </div>
                    <MessageSquare className="w-8 h-8 text-orange-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Total Projects</p>
                      <p className="text-3xl font-bold text-gray-900">{stats.totalProjects}</p>
                    </div>
                    <Calendar className="w-8 h-8 text-orange-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Active Services</p>
                      <p className="text-3xl font-bold text-gray-900">{stats.totalServices}</p>
                    </div>
                    <Award className="w-8 h-8 text-orange-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Published Reviews</p>
                      <p className="text-3xl font-bold text-gray-900">{stats.publishedTestimonials}</p>
                    </div>
                    <Eye className="w-8 h-8 text-orange-600" />
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg">
                    <MessageSquare className="w-5 h-5 text-orange-600" />
                    <div>
                      <p className="font-medium">New testimonial added</p>
                      <p className="text-sm text-gray-600">Dr. Sarah Johnson - 2 hours ago</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg">
                    <Calendar className="w-5 h-5 text-orange-600" />
                    <div>
                      <p className="font-medium">Project updated</p>
                      <p className="text-sm text-gray-600">Medical Conference 2024 - 1 day ago</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Testimonials Management */}
          <TabsContent value="testimonials" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold text-gray-900">Manage Testimonials</h2>
              <Button className="bg-gradient-to-r from-orange-500 to-rose-500 text-white">
                <Plus className="w-4 h-4 mr-2" />
                Add New Testimonial
              </Button>
            </div>

            <div className="grid gap-6">
              {testimonials.map((testimonial) => (
                <Card key={testimonial.id}>
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <h3 className="font-semibold text-lg">{testimonial.name}</h3>
                        <p className="text-orange-600">{testimonial.position}</p>
                        <p className="text-gray-600">{testimonial.company}</p>
                      </div>
                      <div className="flex gap-2">
                        <Button size="sm" variant="outline">
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button size="sm" variant="outline">
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                    <p className="text-gray-700 mb-4 line-clamp-2">{testimonial.text}</p>
                    <div className="flex items-center gap-4 text-sm text-gray-600">
                      <span>Rating: {testimonial.rating}/5</span>
                      <span>Event: {testimonial.event}</span>
                      <span className="capitalize">Status: {testimonial.status}</span>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Projects Management */}
          <TabsContent value="projects" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold text-gray-900">Manage Projects</h2>
              <Button className="bg-gradient-to-r from-orange-500 to-rose-500 text-white">
                <Plus className="w-4 h-4 mr-2" />
                Add New Project
              </Button>
            </div>

            <div className="grid gap-6">
              {projects.map((project) => (
                <Card key={project.id}>
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <h3 className="font-semibold text-lg">{project.title}</h3>
                        <p className="text-orange-600">{project.category}</p>
                        <p className="text-gray-600">
                          {project.location} • {project.date}
                        </p>
                      </div>
                      <div className="flex gap-2">
                        <Button size="sm" variant="outline">
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button size="sm" variant="outline">
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                    <p className="text-gray-700 mb-4 line-clamp-2">{project.description}</p>
                    <div className="flex items-center gap-4 text-sm text-gray-600">
                      <span>Attendees: {project.attendees}</span>
                      <span className="capitalize">Status: {project.status}</span>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Services Management */}
          <TabsContent value="services" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold text-gray-900">Manage Services</h2>
              <Button className="bg-gradient-to-r from-orange-500 to-rose-500 text-white">
                <Plus className="w-4 h-4 mr-2" />
                Add New Service
              </Button>
            </div>

            <div className="grid gap-6">
              {services.map((service) => (
                <Card key={service.id}>
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <h3 className="font-semibold text-lg">{service.title}</h3>
                        <p className="text-orange-600">{service.pricing}</p>
                      </div>
                      <div className="flex gap-2">
                        <Button size="sm" variant="outline">
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button size="sm" variant="outline">
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                    <p className="text-gray-700 mb-4 line-clamp-2">{service.description}</p>
                    <div className="flex flex-wrap gap-2 mb-4">
                      {service.features.slice(0, 3).map((feature, index) => (
                        <span key={index} className="bg-orange-100 text-orange-700 px-2 py-1 rounded text-sm">
                          {feature}
                        </span>
                      ))}
                    </div>
                    <div className="text-sm text-gray-600">
                      <span className="capitalize">Status: {service.status}</span>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Settings */}
          <TabsContent value="settings" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Website Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Company Name</label>
                    <Input defaultValue="OrangeRose" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                    <Input defaultValue="+91 8128338581" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                    <Input defaultValue="<EMAIL>" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Working Hours</label>
                    <Input defaultValue="10am - 6pm" />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Company Description</label>
                  <Textarea
                    rows={4}
                    defaultValue="Professional event management company specializing in conferences, exhibitions, and corporate events."
                  />
                </div>
                <Button className="bg-gradient-to-r from-orange-500 to-rose-500 text-white">
                  <Save className="w-4 h-4 mr-2" />
                  Save Settings
                </Button>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
