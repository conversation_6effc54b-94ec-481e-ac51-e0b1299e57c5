import Navigation from "@/components/navigation"
import Footer from "@/components/footer"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import {
  Users,
  Calendar,
  Award,
  Presentation,
  Globe,
  Mic,
  CheckCircle,
  ArrowRight,
  Star,
  Clock,
  Target,
} from "lucide-react"

export default function ServicesPage() {
  const services = [
    {
      title: "Conference Management",
      description:
        "Comprehensive conference planning and execution services for medical, scientific, and corporate conferences with end-to-end management.",
      icon: Users,
      features: [
        "Advisory Board Meetings",
        "Investigator Meetings",
        "MICE Programme",
        "CME Programs",
        "Scientific Conferences",
        "Medical Symposiums",
        "Delegate Management",
        "Speaker Coordination",
      ],
      pricing: "Starting from ₹2,50,000",
      popular: true,
      color: "from-blue-500 to-blue-600",
    },
    {
      title: "Exhibition Management",
      description:
        "End-to-end exhibition services from planning to execution, ensuring maximum ROI for exhibitors and engaging experiences for visitors.",
      icon: Calendar,
      features: [
        "Sales and Marketing",
        "Operation Management",
        "Visitor Promotion",
        "National & International",
        "Booth Design & Setup",
        "Lead Generation",
        "Exhibitor Services",
        "Floor Plan Management",
      ],
      pricing: "Starting from ₹5,00,000",
      popular: false,
      color: "from-green-500 to-green-600",
    },
    {
      title: "Corporate Events",
      description:
        "Professional corporate event management for product launches, annual meetings, team building, and business gatherings.",
      icon: Award,
      features: [
        "International Speaker Program",
        "Product Launch Events",
        "Live Workshops",
        "Team Building",
        "Annual Meetings",
        "Award Ceremonies",
        "Board Meetings",
        "Corporate Training",
      ],
      pricing: "Starting from ₹1,50,000",
      popular: false,
      color: "from-purple-500 to-purple-600",
    },
    {
      title: "Speaker Management",
      description:
        "Complete speaker coordination services including international speakers, expert panels, and comprehensive speaker support.",
      icon: Mic,
      features: [
        "International Speakers",
        "Panel Discussions",
        "Keynote Coordination",
        "Speaker Support",
        "Travel Arrangements",
        "Content Coordination",
        "Technical Rehearsals",
        "Speaker Hospitality",
      ],
      pricing: "Starting from ₹75,000",
      popular: false,
      color: "from-orange-500 to-rose-500",
    },
    {
      title: "Event Marketing & Promotion",
      description:
        "Strategic marketing and promotion services to maximize event attendance, engagement, and brand visibility.",
      icon: Globe,
      features: [
        "Digital Marketing",
        "Social Media Promotion",
        "Registration Campaigns",
        "Brand Partnerships",
        "PR & Media Relations",
        "Content Creation",
        "Email Marketing",
        "Influencer Outreach",
      ],
      pricing: "Starting from ₹1,00,000",
      popular: false,
      color: "from-pink-500 to-pink-600",
    },
    {
      title: "Technical & AV Support",
      description:
        "Complete technical infrastructure management for seamless event execution with latest technology and equipment.",
      icon: Presentation,
      features: [
        "AV Equipment",
        "Live Streaming",
        "Registration Systems",
        "Event Apps",
        "Lighting & Sound",
        "Technical Crew",
        "Broadcast Services",
        "Virtual Event Platform",
      ],
      pricing: "Starting from ₹50,000",
      popular: false,
      color: "from-indigo-500 to-indigo-600",
    },
  ]

  const additionalServices = [
    {
      title: "Venue Selection & Management",
      description: "Expert venue sourcing and comprehensive management services",
      icon: Target,
    },
    {
      title: "Catering & Hospitality",
      description: "Premium catering and hospitality arrangements for all events",
      icon: Star,
    },
    {
      title: "Transportation & Logistics",
      description: "Comprehensive transportation and logistics coordination",
      icon: Globe,
    },
    {
      title: "Accommodation Management",
      description: "Hotel bookings and accommodation arrangements",
      icon: Clock,
    },
    {
      title: "Branding & Signage",
      description: "Event branding, signage, and visual identity development",
      icon: Award,
    },
    {
      title: "Photography & Videography",
      description: "Professional event documentation and media services",
      icon: Presentation,
    },
  ]

  const process = [
    {
      step: "01",
      title: "Discovery & Consultation",
      description: "We begin with understanding your objectives, audience, budget, and vision for the event.",
      icon: Users,
    },
    {
      step: "02",
      title: "Strategic Planning",
      description:
        "Our team creates a comprehensive plan covering all aspects including timeline, budget, and logistics.",
      icon: Calendar,
    },
    {
      step: "03",
      title: "Design & Development",
      description: "We develop the event concept, design elements, and coordinate all pre-event preparations.",
      icon: Presentation,
    },
    {
      step: "04",
      title: "Execution & Management",
      description: "On-site management ensuring flawless execution with real-time problem solving and coordination.",
      icon: CheckCircle,
    },
    {
      step: "05",
      title: "Post-Event Analysis",
      description: "Comprehensive post-event analysis, feedback collection, and reporting for continuous improvement.",
      icon: Star,
    },
  ]

  const stats = [
    { number: "500+", label: "Events Delivered", icon: Award },
    { number: "15+", label: "Service Categories", icon: Star },
    { number: "98%", label: "Client Satisfaction", icon: CheckCircle },
    { number: "24/7", label: "Support Available", icon: Clock },
  ]

  return (
    <main className="min-h-screen">
      <Navigation />

      {/* Hero Section */}
      <section className="relative pt-40 pb-24 bg-gradient-to-br from-orange-50 via-rose-50 to-amber-50 overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-orange-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-rose-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse delay-1000"></div>
        </div>

        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 bg-white/80 backdrop-blur-sm rounded-full px-6 py-3 text-sm font-medium text-orange-600 border border-orange-200 mb-8">
              <Award className="w-4 h-4" />
              Our Services
            </div>
            <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-bold text-gray-900 mb-8 leading-tight">
              Comprehensive{" "}
              <span className="bg-gradient-to-r from-orange-600 to-rose-600 bg-clip-text text-transparent">
                Event Solutions
              </span>
            </h1>
            <p className="text-xl lg:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed mb-12">
              From intimate corporate meetings to large-scale international conferences, we deliver excellence at every
              level with our comprehensive event management solutions tailored to your specific needs.
            </p>

            {/* Service Stats */}
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 max-w-4xl mx-auto">
              {stats.map((stat, index) => (
                <div key={index} className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg">
                  <stat.icon className="w-8 h-8 text-orange-600 mx-auto mb-3" />
                  <div className="text-3xl font-bold text-gray-900 mb-2">{stat.number}</div>
                  <div className="text-gray-600 font-medium text-sm">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Main Services */}
      <section className="py-24 lg:py-32 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-5xl font-bold text-gray-900 mb-8">
              Our Core{" "}
              <span className="bg-gradient-to-r from-orange-600 to-rose-600 bg-clip-text text-transparent">
                Services
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Specialized event management services designed to meet the unique requirements of different industries and
              event types with professional excellence.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <Card
                key={index}
                className={`group hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-4 bg-white border-0 relative overflow-hidden ${
                  service.popular ? "ring-2 ring-orange-500" : ""
                }`}
              >
                {service.popular && (
                  <div className="absolute top-4 right-4 bg-gradient-to-r from-orange-500 to-rose-500 text-white px-3 py-1 rounded-full text-sm font-semibold z-10">
                    Most Popular
                  </div>
                )}
                <CardContent className="p-8">
                  <div
                    className={`w-16 h-16 bg-gradient-to-r ${service.color} rounded-full flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}
                  >
                    <service.icon className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">{service.title}</h3>
                  <p className="text-gray-600 mb-6 leading-relaxed">{service.description}</p>

                  <div className="mb-6">
                    <h4 className="font-semibold text-gray-900 mb-3">Key Features:</h4>
                    <ul className="space-y-2">
                      {service.features.map((feature, i) => (
                        <li key={i} className="flex items-center text-sm text-gray-600">
                          <CheckCircle className="w-4 h-4 text-green-500 mr-3 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div className="border-t pt-6">
                    <div className="text-2xl font-bold text-orange-600 mb-4">{service.pricing}</div>
                    <Button className="w-full bg-gradient-to-r from-orange-500 to-rose-500 hover:from-orange-600 hover:to-rose-600 text-white rounded-full transition-all duration-300">
                      Get Quote
                      <ArrowRight className="ml-2 w-4 h-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Additional Services */}
      <section className="py-24 lg:py-32 bg-gradient-to-br from-gray-50 to-orange-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-5xl font-bold text-gray-900 mb-8">
              Additional{" "}
              <span className="bg-gradient-to-r from-orange-600 to-rose-600 bg-clip-text text-transparent">
                Services
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Complementary services to ensure every aspect of your event is perfectly managed and executed with
              professional excellence.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {additionalServices.map((service, index) => (
              <Card
                key={index}
                className="group hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-4 bg-white/80 backdrop-blur-sm border-0"
              >
                <CardContent className="p-8 text-center">
                  <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-rose-500 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                    <service.icon className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">{service.title}</h3>
                  <p className="text-gray-600 leading-relaxed">{service.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Our Process */}
      <section className="py-24 lg:py-32 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-5xl font-bold text-gray-900 mb-8">
              Our Proven{" "}
              <span className="bg-gradient-to-r from-orange-600 to-rose-600 bg-clip-text text-transparent">
                Process
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              We follow a systematic, proven approach refined over 15 years to ensure every event is executed flawlessly
              from concept to completion.
            </p>
          </div>

          <div className="relative max-w-6xl mx-auto">
            {/* Process Timeline */}
            <div className="hidden lg:block absolute top-1/2 left-0 right-0 h-1 bg-gradient-to-r from-orange-500 to-rose-500 transform -translate-y-1/2"></div>

            <div className="grid lg:grid-cols-5 gap-8">
              {process.map((step, index) => (
                <div key={index} className="relative">
                  <Card className="bg-gradient-to-br from-orange-50 to-rose-50 border-0 hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-4 relative z-10">
                    <CardContent className="p-8 text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-rose-500 rounded-full flex items-center justify-center mx-auto mb-6 text-white font-bold text-xl">
                        {step.step}
                      </div>
                      <step.icon className="w-8 h-8 text-orange-600 mx-auto mb-4" />
                      <h3 className="text-xl font-semibold text-gray-900 mb-4">{step.title}</h3>
                      <p className="text-gray-600 leading-relaxed text-sm">{step.description}</p>
                    </CardContent>
                  </Card>
                  <div className="hidden lg:block absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-6 h-6 bg-white border-4 border-orange-500 rounded-full z-20"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Industry Expertise */}
      <section className="py-24 lg:py-32 bg-gradient-to-br from-rose-50 to-orange-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-5xl font-bold text-gray-900 mb-8">
              Industry{" "}
              <span className="bg-gradient-to-r from-orange-600 to-rose-600 bg-clip-text text-transparent">
                Expertise
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Specialized knowledge and experience across multiple industries ensures we understand your unique
              requirements and deliver targeted solutions.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                name: "Healthcare & Pharmaceuticals",
                projects: "150+",
                icon: Users,
                color: "from-blue-500 to-blue-600",
              },
              { name: "Technology & Innovation", projects: "120+", icon: Globe, color: "from-green-500 to-green-600" },
              { name: "Finance & Banking", projects: "80+", icon: Award, color: "from-purple-500 to-purple-600" },
              { name: "Education & Research", projects: "90+", icon: Star, color: "from-indigo-500 to-indigo-600" },
              { name: "Manufacturing & Industrial", projects: "70+", icon: Target, color: "from-pink-500 to-pink-600" },
              {
                name: "Government & Public Sector",
                projects: "60+",
                icon: CheckCircle,
                color: "from-orange-500 to-rose-500",
              },
            ].map((industry, index) => (
              <Card
                key={index}
                className="group hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-4 bg-white/80 backdrop-blur-sm border-0"
              >
                <CardContent className="p-8 text-center">
                  <div
                    className={`w-16 h-16 bg-gradient-to-r ${industry.color} rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300`}
                  >
                    <industry.icon className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">{industry.name}</h3>
                  <div className="text-3xl font-bold text-orange-600 mb-2">{industry.projects}</div>
                  <div className="text-sm text-gray-600">Projects Completed</div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 lg:py-32 bg-gradient-to-r from-orange-500 to-rose-500">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl lg:text-5xl font-bold text-white mb-8">Ready to Create Your Next Event?</h2>
          <p className="text-xl text-white/90 mb-12 max-w-2xl mx-auto leading-relaxed">
            Let's discuss your requirements and create an unforgettable experience for your audience with our
            professional services and expertise.
          </p>
          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <Button
              size="lg"
              className="bg-white text-orange-600 hover:bg-gray-100 px-10 py-4 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg font-semibold text-lg"
            >
              Get Free Consultation
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="border-2 border-white text-white hover:bg-white hover:text-orange-600 px-10 py-4 rounded-full transition-all duration-300 bg-transparent font-semibold text-lg"
            >
              View Our Portfolio
            </Button>
          </div>
        </div>
      </section>

      <Footer />
    </main>
  )
}
