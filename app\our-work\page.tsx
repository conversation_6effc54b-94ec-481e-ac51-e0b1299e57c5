import Navigation from "@/components/navigation"
import Footer from "@/components/footer"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Award, Users, Globe, TrendingUp, Star, CheckCircle, Calendar, Target, Clock, Lightbulb } from "lucide-react"
import Image from "next/image"

export default function OurWorkPage() {
  const achievements = [
    {
      icon: Award,
      number: "500+",
      label: "Events Delivered",
      description: "Successfully organized events across various industries and formats with excellence",
      color: "from-blue-500 to-blue-600",
    },
    {
      icon: Users,
      number: "100K+",
      label: "Attendees Served",
      description: "Connected professionals and industry leaders worldwide through our events",
      color: "from-green-500 to-green-600",
    },
    {
      icon: Globe,
      number: "50+",
      label: "Cities Covered",
      description: "Pan-India presence with international collaborations and partnerships",
      color: "from-purple-500 to-purple-600",
    },
    {
      icon: TrendingUp,
      number: "98%",
      label: "Client Satisfaction",
      description: "Consistently exceeding client expectations and delivering exceptional results",
      color: "from-orange-500 to-rose-500",
    },
  ]

  const capabilities = [
    "End-to-end event planning and strategic execution",
    "International speaker coordination and management",
    "Advanced registration and ticketing systems",
    "Live streaming and hybrid event solutions",
    "Professional audio-visual production services",
    "Comprehensive marketing and promotion campaigns",
    "Real-time event analytics and detailed reporting",
    "Post-event follow-up and engagement strategies",
    "Venue sourcing and contract negotiation",
    "Catering and hospitality management",
    "Transportation and logistics coordination",
    "Branding and visual identity development",
  ]

  const industries = [
    {
      name: "Healthcare & Pharmaceuticals",
      projects: 150,
      description: "Medical conferences, pharmaceutical exhibitions, and healthcare summits with regulatory compliance",
      icon: Users,
      specialties: ["CME Programs", "Regulatory Conferences", "Medical Device Launches"],
      color: "from-blue-500 to-blue-600",
    },
    {
      name: "Technology & Innovation",
      projects: 120,
      description: "Tech summits, product launches, and innovation showcases with cutting-edge solutions",
      icon: Globe,
      specialties: ["Product Launches", "Developer Conferences", "Innovation Summits"],
      color: "from-green-500 to-green-600",
    },
    {
      name: "Finance & Banking",
      projects: 80,
      description: "Financial conferences, banking seminars, and investment forums with high-level security",
      icon: Award,
      specialties: ["Investment Forums", "Banking Summits", "Fintech Events"],
      color: "from-purple-500 to-purple-600",
    },
    {
      name: "Education & Research",
      projects: 90,
      description: "Academic conferences, research symposiums, and educational workshops",
      icon: Star,
      specialties: ["Academic Conferences", "Research Symposiums", "Educational Workshops"],
      color: "from-indigo-500 to-indigo-600",
    },
    {
      name: "Manufacturing & Industrial",
      projects: 70,
      description: "Industrial exhibitions, manufacturing summits, and trade shows",
      icon: Target,
      specialties: ["Trade Shows", "Industrial Exhibitions", "Manufacturing Summits"],
      color: "from-pink-500 to-pink-600",
    },
    {
      name: "Government & Public Sector",
      projects: 60,
      description: "Government events, public forums, and policy conferences with protocol management",
      icon: CheckCircle,
      specialties: ["Policy Conferences", "Public Forums", "Government Summits"],
      color: "from-orange-500 to-rose-500",
    },
  ]

  const testimonialHighlights = [
    {
      quote:
        "OrangeRose delivered an exceptional conference that exceeded all expectations. Their attention to detail was remarkable.",
      author: "Dr. Rajesh Kumar",
      company: "Global Health Solutions",
      rating: 5,
      event: "International Medical Conference 2024",
      image: "/placeholder.svg?height=80&width=80",
    },
    {
      quote: "The exhibition was a tremendous success. Professional execution resulted in our highest ROI to date.",
      author: "Priya Sharma",
      company: "TechCorp Industries",
      rating: 5,
      event: "Technology Innovation Expo 2024",
      image: "/placeholder.svg?height=80&width=80",
    },
    {
      quote: "Outstanding event management. The team's creativity and execution were phenomenal throughout.",
      author: "Amit Patel",
      company: "Innovation Labs",
      rating: 5,
      event: "Corporate Innovation Summit 2024",
      image: "/placeholder.svg?height=80&width=80",
    },
  ]

  const workProcess = [
    {
      phase: "Discovery",
      description: "Understanding your objectives, audience, and requirements through detailed consultation",
      duration: "1-2 weeks",
      deliverables: ["Requirement Analysis", "Audience Profiling", "Objective Setting"],
      icon: Users,
    },
    {
      phase: "Strategy",
      description: "Developing comprehensive event strategy and detailed planning framework",
      duration: "2-3 weeks",
      deliverables: ["Event Strategy", "Timeline Planning", "Budget Allocation"],
      icon: Target,
    },
    {
      phase: "Design",
      description: "Creating event concept, branding, and immersive experience design",
      duration: "2-4 weeks",
      deliverables: ["Event Concept", "Visual Identity", "Experience Design"],
      icon: Lightbulb,
    },
    {
      phase: "Execution",
      description: "Managing all aspects of event delivery and real-time coordination",
      duration: "Event Duration",
      deliverables: ["On-site Management", "Real-time Coordination", "Quality Assurance"],
      icon: CheckCircle,
    },
    {
      phase: "Analysis",
      description: "Post-event analysis, reporting, and continuous improvement recommendations",
      duration: "1-2 weeks",
      deliverables: ["Event Report", "ROI Analysis", "Feedback Summary"],
      icon: TrendingUp,
    },
  ]

  const stats = [
    { number: "15+", label: "Years Experience", icon: Clock },
    { number: "500+", label: "Events Delivered", icon: Award },
    { number: "50+", label: "Cities Served", icon: Globe },
    { number: "98%", label: "Success Rate", icon: Star },
  ]

  return (
    <main className="min-h-screen">
      <Navigation />

      {/* Hero Section */}
      <section className="relative pt-40 pb-24 bg-gradient-to-br from-orange-50 via-rose-50 to-amber-50 overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-orange-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-rose-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse delay-1000"></div>
        </div>

        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 bg-white/80 backdrop-blur-sm rounded-full px-6 py-3 text-sm font-medium text-orange-600 border border-orange-200 mb-8 animate-fade-in-up">
              <Star className="w-4 h-4" />
              Our Excellence
            </div>
            <h1
              className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-bold text-gray-900 mb-8 leading-tight animate-fade-in-up"
              style={{ animationDelay: "200ms" }}
            >
              Our{" "}
              <span className="bg-gradient-to-r from-orange-600 to-rose-600 bg-clip-text text-transparent">
                Work & Expertise
              </span>
            </h1>
            <p
              className="text-xl lg:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed mb-12 animate-fade-in-up"
              style={{ animationDelay: "400ms" }}
            >
              With over 15 years of experience and a proven track record of 500+ successful events, we have established
              ourselves as India's leading event management company, delivering exceptional results across diverse
              industries.
            </p>

            {/* Work Highlights */}
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 max-w-4xl mx-auto">
              {stats.map((stat, index) => (
                <div
                  key={index}
                  className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg animate-fade-in-up"
                  style={{ animationDelay: `${600 + index * 100}ms` }}
                >
                  <stat.icon className="w-8 h-8 text-orange-600 mx-auto mb-3" />
                  <div className="text-3xl font-bold text-orange-600 mb-2">{stat.number}</div>
                  <div className="text-gray-600 font-medium text-sm">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Achievements */}
      <section className="py-24 lg:py-32 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-5xl font-bold text-gray-900 mb-8">
              Our{" "}
              <span className="bg-gradient-to-r from-orange-600 to-rose-600 bg-clip-text text-transparent">
                Achievements
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Numbers that reflect our commitment to excellence, innovation, and client satisfaction in event management
              across all industries.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {achievements.map((achievement, index) => (
              <Card
                key={index}
                className="text-center group hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-4 bg-white border-0"
              >
                <CardContent className="p-8 lg:p-10">
                  <div
                    className={`w-20 h-20 bg-gradient-to-r ${achievement.color} rounded-full flex items-center justify-center mx-auto mb-8 group-hover:scale-110 transition-transform duration-300`}
                  >
                    <achievement.icon className="w-10 h-10 text-white" />
                  </div>
                  <div className="text-4xl font-bold text-gray-900 mb-3">{achievement.number}</div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">{achievement.label}</h3>
                  <p className="text-gray-600 text-sm leading-relaxed">{achievement.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Our Process */}
      <section className="py-24 lg:py-32 bg-gradient-to-br from-gray-50 to-orange-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-5xl font-bold text-gray-900 mb-8">
              Our Proven{" "}
              <span className="bg-gradient-to-r from-orange-600 to-rose-600 bg-clip-text text-transparent">
                Work Process
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              A systematic approach refined over 15 years to ensure every event achieves its objectives and exceeds
              expectations with measurable results.
            </p>
          </div>

          <div className="relative max-w-6xl mx-auto">
            {/* Process Timeline */}
            <div className="hidden lg:block absolute top-1/2 left-0 right-0 h-1 bg-gradient-to-r from-orange-500 to-rose-500 transform -translate-y-1/2"></div>

            <div className="grid lg:grid-cols-5 gap-8">
              {workProcess.map((phase, index) => (
                <div key={index} className="relative">
                  <Card className="bg-white border-0 shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-4 relative z-10">
                    <CardContent className="p-8 text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-rose-500 rounded-full flex items-center justify-center mx-auto mb-6 text-white font-bold text-xl">
                        {index + 1}
                      </div>
                      <phase.icon className="w-8 h-8 text-orange-600 mx-auto mb-4" />
                      <h3 className="text-xl font-semibold text-gray-900 mb-4">{phase.phase}</h3>
                      <p className="text-gray-600 mb-4 text-sm leading-relaxed">{phase.description}</p>
                      <div className="text-orange-600 font-medium text-sm mb-4">{phase.duration}</div>
                      <div className="space-y-1">
                        {phase.deliverables.map((deliverable, i) => (
                          <div
                            key={i}
                            className="text-xs text-gray-500 bg-gray-100 rounded-full px-3 py-1 inline-block mr-1 mb-1"
                          >
                            {deliverable}
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                  <div className="hidden lg:block absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-6 h-6 bg-white border-4 border-orange-500 rounded-full z-20"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Industries We Serve */}
      <section className="py-24 lg:py-32 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-5xl font-bold text-gray-900 mb-8">
              Industries{" "}
              <span className="bg-gradient-to-r from-orange-600 to-rose-600 bg-clip-text text-transparent">
                We Serve
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Deep industry expertise and specialized knowledge across multiple sectors, ensuring we understand your
              unique requirements and deliver targeted solutions.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {industries.map((industry, index) => (
              <Card
                key={index}
                className="group hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-4 bg-white border-0"
              >
                <CardContent className="p-8">
                  <div className="flex items-center justify-between mb-6">
                    <div
                      className={`w-16 h-16 bg-gradient-to-r ${industry.color} rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}
                    >
                      <industry.icon className="w-8 h-8 text-white" />
                    </div>
                    <div className="text-right">
                      <div className="text-3xl font-bold text-orange-600">{industry.projects}+</div>
                      <div className="text-sm text-gray-600">Projects</div>
                    </div>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">{industry.name}</h3>
                  <p className="text-gray-600 leading-relaxed mb-6">{industry.description}</p>
                  <div>
                    <h4 className="font-medium text-gray-900 mb-3">Specializations:</h4>
                    <div className="flex flex-wrap gap-2">
                      {industry.specialties.map((specialty, i) => (
                        <span key={i} className="text-sm bg-orange-100 text-orange-700 rounded-full px-3 py-1">
                          {specialty}
                        </span>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Our Capabilities */}
      <section className="py-24 lg:py-32 bg-gradient-to-br from-rose-50 to-orange-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-16 lg:gap-24 items-center">
            <div>
              <h2 className="text-3xl lg:text-5xl font-bold text-gray-900 mb-8">
                Our Core{" "}
                <span className="bg-gradient-to-r from-orange-600 to-rose-600 bg-clip-text text-transparent">
                  Capabilities
                </span>
              </h2>
              <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                We offer comprehensive event management services with cutting-edge technology, innovative solutions, and
                a dedicated team of professionals to ensure your events are successful, memorable, and impactful.
              </p>

              <div className="grid grid-cols-1 gap-4">
                {capabilities.map((capability, index) => (
                  <div
                    key={index}
                    className="flex items-start gap-4 p-4 bg-white/80 backdrop-blur-sm rounded-xl hover:shadow-md transition-all duration-300 hover:transform hover:translate-x-2"
                  >
                    <CheckCircle className="w-6 h-6 text-green-500 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700 leading-relaxed">{capability}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="relative">
              <div className="aspect-square bg-gradient-to-br from-orange-200 to-rose-200 rounded-3xl overflow-hidden">
                <Image
                  src="/placeholder.svg?height=600&width=600"
                  alt="Our Capabilities"
                  width={600}
                  height={600}
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="absolute -bottom-8 -left-8 bg-white rounded-2xl p-6 shadow-2xl">
                <div className="flex items-center gap-4">
                  <Award className="w-10 h-10 text-orange-600" />
                  <div>
                    <div className="font-bold text-gray-900 text-lg">15+ Years</div>
                    <div className="text-gray-600">Experience</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Client Testimonials */}
      <section className="py-24 lg:py-32 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-5xl font-bold text-gray-900 mb-8">
              What Our{" "}
              <span className="bg-gradient-to-r from-orange-600 to-rose-600 bg-clip-text text-transparent">
                Clients Say
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Real feedback from real clients who have experienced the OrangeRose difference in event management and
              professional service delivery.
            </p>
          </div>

          <div className="grid lg:grid-cols-3 gap-8">
            {testimonialHighlights.map((testimonial, index) => (
              <Card
                key={index}
                className="group hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-4 bg-white border-0"
              >
                <CardContent className="p-8">
                  <div className="flex items-center gap-4 mb-6">
                    <Image
                      src={testimonial.image || "/placeholder.svg"}
                      alt={testimonial.author}
                      width={80}
                      height={80}
                      className="w-16 h-16 rounded-full object-cover border-2 border-orange-100"
                    />
                    <div>
                      <h4 className="font-semibold text-gray-900">{testimonial.author}</h4>
                      <p className="text-orange-600 text-sm font-medium">{testimonial.company}</p>
                      <div className="flex mt-1">
                        {[...Array(testimonial.rating)].map((_, i) => (
                          <Star key={i} className="w-4 h-4 fill-orange-400 text-orange-400" />
                        ))}
                      </div>
                    </div>
                  </div>

                  <blockquote className="text-gray-700 mb-4 leading-relaxed italic text-lg">
                    "{testimonial.quote}"
                  </blockquote>

                  <div className="flex items-center gap-2 text-sm text-gray-500">
                    <Calendar className="w-4 h-4" />
                    <span>{testimonial.event}</span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-24 lg:py-32 bg-gradient-to-r from-orange-500 to-rose-500">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl lg:text-5xl font-bold text-white mb-8">
            Ready to Experience the OrangeRose Difference?
          </h2>
          <p className="text-xl text-white/90 mb-12 max-w-2xl mx-auto leading-relaxed">
            Let's collaborate to create an exceptional event that achieves your objectives and leaves a lasting impact
            on your audience. Our proven expertise is ready to serve you.
          </p>
          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <Button
              size="lg"
              className="bg-white text-orange-600 hover:bg-gray-100 px-10 py-4 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg font-semibold text-lg"
            >
              Start Your Project
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="border-2 border-white text-white hover:bg-white hover:text-orange-600 px-10 py-4 rounded-full transition-all duration-300 bg-transparent font-semibold text-lg"
            >
              View Our Portfolio
            </Button>
          </div>
        </div>
      </section>

      <Footer />
    </main>
  )
}
